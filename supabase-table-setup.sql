-- SQL for Supabase table creation for ROTI-RAGIL-INVOICE

-- 1. Products Table
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nama text NOT NULL,
  harga numeric NOT NULL,
  isDefault boolean DEFAULT false,
  createdat timestamptz DEFAULT now()
);

-- 2. Customers Table
CREATE TABLE IF NOT EXISTS customers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nama text NOT NULL,
  alamat text,
  telepon text,
  email text,
  isDefault boolean DEFAULT false,
  createdat timestamptz DEFAULT now()
);

-- 3. Invoices Table
CREATE TABLE IF NOT EXISTS invoices (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nomorInvoice text NOT NULL,
  pelangganId uuid REFERENCES customers(id) ON DELETE SET NULL,
  items jsonb,
  total numeric,
  status text,
  paidat timestamptz,
  createdat timestamptz DEFAULT now()
);

-- 4. (Optional) Expenses Table
CREATE TABLE IF NOT EXISTS expenses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  amount numeric NOT NULL,
  description text,
  createdat timestamptz DEFAULT now()
);

-- 5. Bahan Baku Table
CREATE TABLE IF NOT EXISTS bahan_baku (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nama text NOT NULL,
  satuan text,
  stok numeric,
  harga numeric,
  createdat timestamptz DEFAULT now()
);

-- 6. HPP Table
CREATE TABLE IF NOT EXISTS hpp (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  produk_id uuid REFERENCES products(id) ON DELETE SET NULL,
  bahan_baku_id uuid REFERENCES bahan_baku(id) ON DELETE SET NULL,
  jumlah numeric,
  harga numeric,
  createdat timestamptz DEFAULT now()
);
