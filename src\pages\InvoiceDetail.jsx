import React, { useState, useEffect } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { ArrowLeft, Download, Edit, Image, FileText } from 'lucide-react'
import { getInvoiceById, getCustomers, getProducts } from '../services/storage'
import { generatePDF } from '../services/pdfService'
import { generateInvoiceImage } from '../services/imageService'
import ModernButton from '../components/ModernButton'

const InvoiceDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [invoice, setInvoice] = useState(null)
  const [customer, setCustomer] = useState(null)
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [id])

  const loadData = async () => {
    const invoiceData = getInvoiceById(id)
    const productsData = getProducts()
    const customersData = getCustomers()

    if (invoiceData) {
      // Sort items by date before setting the state
      const sortedItems = [...invoiceData.items].sort((a, b) => new Date(a.createdat) - new Date(b.createdat))
      setInvoice({
        ...invoiceData,
        items: sortedItems
      })
      const customerData = customersData.find(c => c.id === invoiceData.pelangganid)
      setCustomer(customerData)
      setProducts(productsData)
    }
    
    setLoading(false)
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-emerald-100 text-emerald-800'  
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return 'Lunas'
      case 'sent':
        return 'Terkirim'
      default:
        return 'Draft'
    }
  }

  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId)
    return product ? product.nama : 'Produk Tidak Ditemukan'
  }

  const handleDownloadPDF = async () => {
    if (invoice && customer) {
      await generatePDF(invoice, customer)
    }
  }

  const handleDownloadImage = async () => {
    if (invoice && customer) {
      // Create a temporary invoice detail element for image generation
      const tempDiv = document.createElement('div')
      tempDiv.id = 'temp-invoice-content'
      tempDiv.style.position = 'absolute'
      tempDiv.style.left = '-9999px'
      tempDiv.style.width = '800px'
      tempDiv.style.backgroundColor = 'white'
      tempDiv.style.padding = '20px'
      tempDiv.style.fontFamily = 'Inter, system-ui, sans-serif'

      // Create invoice HTML content
      tempDiv.innerHTML = `
        <div style="border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; background: white;">
          <!-- Header -->
          <div style="padding: 24px; border-bottom: 1px solid #f3f4f6; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
            <div style="display: flex; justify-content: space-between; align-items: start;">
              <div style="text-align: left;">
                <h1 style="font-size: 28px; font-weight: bold; color: #059669; margin: 0 0 12px 0;">ROTI RAGIL</h1>
                <div style="margin: 8px 0;">
                  <p style="font-size: 16px; font-weight: 600; color: #1f2937; background: #fef3c7; padding: 6px 16px; border-radius: 8px; border: 1px solid #f59e0b; display: inline-block;">No. P-IRT: 2053471011676-30</p>
                </div>
                <p style="font-size: 15px; font-weight: 500; color: #374151; margin: 8px 0 0 0; text-align: left;">Telp: 0895402652626</p>
              </div>
              <div style="text-align: right;">
                <h2 style="font-size: 24px; font-weight: bold; color: #374151; margin: 0 0 8px 0;">INVOICE</h2>
                <p style="font-size: 14px; color: #6b7280; margin: 0;">No: ${invoice.nomorinvoice}</p>
                <p style="font-size: 14px; color: #6b7280; margin: 0;">Tanggal: ${formatDate(invoice.createdat)}</p>
              </div>
            </div>
          </div>

          <!-- Customer -->
          <div style="padding: 24px; border-bottom: 1px solid #f3f4f6;">
            <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin: 0 0 12px 0;">Kepada:</h3>
            <div style="background: #f9fafb; padding: 16px; border-radius: 8px;">
              <p style="font-size: 18px; font-weight: 600; color: #374151; margin: 0;">${customer.nama}</p>
            </div>
          </div>

          <!-- Items -->
          <div style="padding: 24px;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);">
                  <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">No</th>
                  <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Produk</th>
                  <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Tanggal</th>
                  <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Qty</th>
                  <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Harga</th>
                  <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Total</th>
                </tr>
              </thead>
              <tbody>
                ${invoice.items.map((item, index) => {
                  const productName = getProductName(item.produkId)
                  const itemDate = item.createdat ? new Date(item.createdat).toLocaleDateString('id-ID') : '-'
                  return `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                      <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${index + 1}</td>
                      <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${productName}</td>
                      <td style="padding: 16px; font-size: 14px; color: #6b7280; text-align: center;">${itemDate}</td>
                      <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151; text-align: center;">${item.quantity}</td>
                      <td style="padding: 16px; font-size: 14px; color: #111827; text-align: right;">${formatCurrency(item.harga)}</td>
                      <td style="padding: 16px; font-size: 14px; font-weight: 600; color: #111827; text-align: right;">${formatCurrency(item.quantity * item.harga)}</td>
                    </tr>
                  `
                }).join('')}
              </tbody>
            </table>
          </div>

          <!-- Total -->
          <div style="padding: 24px; border-top: 1px solid #f3f4f6; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <p style="font-size: 14px; font-weight: 500; color: #374151; margin: 0;">Terima kasih atas kepercayaan Anda!</p>
                <p style="font-size: 14px; color: #6b7280; margin: 0;">Pembayaran dapat dilakukan melalui transfer bank atau tunai.</p>
              </div>
              <div style="text-align: right;">
                <p style="font-size: 14px; color: #6b7280; margin: 0 0 4px 0;">Total Pembayaran</p>
                <p style="font-size: 24px; font-weight: bold; color: #059669; margin: 0;">${formatCurrency(invoice.total)}</p>
              </div>
            </div>
          </div>
        </div>
      `

      document.body.appendChild(tempDiv)

      try {
        await generateInvoiceImage(invoice, customer, 'temp-invoice-content')
      } finally {
        document.body.removeChild(tempDiv)
      }
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Invoice tidak ditemukan</h3>
        <p className="mt-1 text-sm text-gray-500">
          Invoice yang Anda cari tidak ada atau telah dihapus.
        </p>
        <div className="mt-6">
          <button
            onClick={() => navigate('/invoices')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Daftar Invoice
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      {/* Header */}
      <div className="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-3">
        <button
          onClick={() => navigate('/invoices')}
          className="btn btn-link text-muted p-0 d-flex align-items-center text-decoration-none"
        >
          <ArrowLeft className="h-4 w-4 me-1" />
          Kembali ke Daftar Invoice
        </button>
        <div className="d-flex flex-column flex-sm-row gap-2">
          <Link
            to={`/invoices/${id}/edit`}
            className="btn btn-outline-secondary btn-sm d-flex align-items-center justify-content-center gap-2"
          >
            <Edit className="h-4 w-4" />
            Edit Invoice
          </Link>
          <button
            onClick={handleDownloadImage}
            className="btn btn-outline-info btn-sm d-flex align-items-center justify-content-center gap-2"
          >
            <Image className="h-4 w-4" />
            Export Gambar
          </button>
        </div>
      </div>

      {/* Invoice Detail */}
      <div id="invoice-content" className="card overflow-hidden">
        {/* Header Section */}
        <div className="card-header">
          <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            <div className="space-y-2">
              <h1 className="text-2xl md:text-3xl font-bold text-emerald-600">
                ROTI RAGIL
              </h1>
              <div className="flex justify-center md:justify-start">
                <p className="text-base md:text-lg font-semibold text-gray-800 bg-yellow-50 px-4 py-2 rounded-lg border border-yellow-200 inline-block">
                  No. P-IRT: 2053471011676-30
                </p>
              </div>
              <p className="text-sm md:text-base text-gray-700 font-medium text-center md:text-left">Telp: 0895402652626</p>
            </div>
            <div className="text-left md:text-right space-y-2">
              <h2 className="text-xl md:text-2xl font-bold text-gray-800">INVOICE</h2>
              <div className="space-y-1">
                <p className="text-sm text-gray-800">No: <span className="font-semibold text-gray-900">{invoice.nomorinvoice}</span></p>
                <p className="text-sm text-gray-800">Tanggal: <span className="font-semibold text-gray-900">{formatDate(invoice.createdat)}</span></p>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold ${getStatusColor(invoice.status)}`}>
                  {getStatusText(invoice.status)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Info - Simplified */}
        <div className="px-6 py-4 border-b border-gray-100">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Kepada:</h3>
          {customer ? (
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="font-semibold text-gray-900 text-lg">{customer.nama}</p>
            </div>
          ) : (
            <p className="text-sm text-gray-500">Data pelanggan tidak ditemukan</p>
          )}
        </div>

        {/* Items Table - Desktop */}
        <div className="hidden md:block">
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead className="table-light">
                <tr>
                  <th scope="col">No</th>
                  <th scope="col">Produk</th>
                  <th scope="col" className="text-center">Tanggal</th>
                  <th scope="col" className="text-center">Qty</th>
                  <th scope="col" className="text-end">Harga</th>
                  <th scope="col" className="text-end">Total</th>
                </tr>
              </thead>
              <tbody>
                {invoice.items.map((item, index) => (
                  <tr key={index}>
                    <td className="fw-medium">{index + 1}</td>
                    <td className="fw-medium">{getProductName(item.produkId)}</td>
                    <td className="text-center text-muted">
                      {item.createdat ? new Date(item.createdat).toLocaleDateString('id-ID') : '-'}
                    </td>
                    <td className="text-center fw-medium">{item.quantity}</td>
                    <td className="text-end">{formatCurrency(item.harga)}</td>
                    <td className="text-end fw-bold text-success">
                      {formatCurrency(item.quantity * item.harga)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Items Cards - Mobile */}
        <div className="d-md-none p-3">
          {invoice.items.map((item, index) => (
            <div key={index} className="card mb-3 border-light">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-start mb-2">
                  <div className="flex-grow-1">
                    <h6 className="card-title fw-semibold mb-1">{getProductName(item.produkId)}</h6>
                    <p className="card-text small fw-medium text-muted mb-1">Item #{index + 1}</p>
                    {item.createdat && (
                      <p className="card-text small text-muted">Tanggal: {new Date(item.createdat).toLocaleDateString('id-ID')}</p>
                    )}
                  </div>
                </div>
                <div className="row g-3 small">
                  <div className="col-4">
                    <p className="text-muted fw-medium mb-1">Qty</p>
                    <p className="fw-semibold mb-0">{item.quantity}</p>
                  </div>
                  <div className="col-4">
                    <p className="text-muted fw-medium mb-1">Harga</p>
                    <p className="fw-semibold mb-0">{formatCurrency(item.harga)}</p>
                  </div>
                  <div className="col-4">
                    <p className="text-muted fw-medium mb-1">Total</p>
                    <p className="fw-bold text-success mb-0">{formatCurrency(item.quantity * item.harga)}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Total Section */}
        <div className="px-6 py-6 border-t border-gray-200 bg-gradient-to-r from-emerald-50 to-green-50">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div>
              <p className="font-medium text-gray-700">Terima kasih atas kepercayaan Anda!</p>
              <p className="text-gray-600">Pembayaran dapat dilakukan melalui transfer bank atau tunai.</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-700 mb-1">Total Pembayaran</p>
              <p className="text-2xl font-bold text-emerald-700">
                {formatCurrency(invoice.total)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InvoiceDetail
