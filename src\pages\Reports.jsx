import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  Users, 
  FileText,
  Download,
  Filter
} from 'lucide-react'
import { 
  getRevenueByPeriod, 
  getRevenueTrend, 
  getTopCustomers, 
  getRevenueSummary,
  exportRevenueData,
  getExpenseByPeriod,
  getExpenseTrend,
  getExpenseSummary
} from '../services/reportService'
import { generateInvoiceImage } from '../services/imageService'
import { formatCurrency } from '../utils/formatters'
import ModernButton from '../components/ModernButton'
import ExpenseSummaryCard from '../components/ExpenseSummaryCard'

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [customDateRange, setCustomDateRange] = useState({
    start: '',
    end: ''
  })
  const [useCustomRange, setUseCustomRange] = useState(false)
  const [reportData, setReportData] = useState(null)
  const [trendData, setTrendData] = useState([])
  const [topCustomers, setTopCustomers] = useState([])
  const [summary, setSummary] = useState(null)
  // Expense states
  const [expenseData, setExpenseData] = useState(null)
  const [expenseTrend, setExpenseTrend] = useState([])
  const [expenseSummary, setExpenseSummary] = useState(null)
  const [loading, setLoading] = useState(true)

  const periodOptions = [
    { value: 'day', label: 'Hari Ini' },
    { value: 'week', label: 'Minggu Ini' },
    { value: 'month', label: 'Bulan Ini' },
    { value: 'year', label: 'Tahun Ini' }
  ]

  const loadReportData = async () => {
    setLoading(true)
    try {
      // Get main report data
      const data = useCustomRange && customDateRange.start && customDateRange.end
        ? await getRevenueByPeriod(selectedPeriod, customDateRange.start, customDateRange.end)
        : await getRevenueByPeriod(selectedPeriod)
      setReportData(data)

      // Get trend data
      const trend = await getRevenueTrend(selectedPeriod, selectedPeriod === 'day' ? 7 : selectedPeriod === 'week' ? 8 : 12)
      setTrendData(trend)

      // Get top customers
      const customers = await getTopCustomers(selectedPeriod)
      setTopCustomers(customers)

      // Get summary
      const summaryData = await getRevenueSummary()
      setSummary(summaryData)

      // --- EXPENSE DATA ---
      const expData = useCustomRange && customDateRange.start && customDateRange.end
        ? await getExpenseByPeriod(selectedPeriod, customDateRange.start, customDateRange.end)
        : await getExpenseByPeriod(selectedPeriod)
      setExpenseData(expData)

      const expTrend = await getExpenseTrend(selectedPeriod, selectedPeriod === 'day' ? 7 : selectedPeriod === 'week' ? 8 : 12)
      setExpenseTrend(expTrend)

      const expSummary = await getExpenseSummary()
      setExpenseSummary(expSummary)

    } catch (error) {
      console.error('Error loading report data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadReportData()
  }, [selectedPeriod, useCustomRange, customDateRange])

  const handleExportData = async () => {
    const exportData = await exportRevenueData(selectedPeriod)
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `laporan-pendapatan-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleExportImage = async () => {
    try {
      // Sembunyikan tombol export saat capture
      const exportButtons = document.querySelectorAll('.export-buttons')
      exportButtons.forEach(btn => btn.style.display = 'none')
      await generateInvoiceImage({ nomorInvoice: 'Laporan' }, {}, 'report-content')
      exportButtons.forEach(btn => btn.style.display = '')
    } catch (e) {
      // fallback error toast sudah di imageService
    }
  }

  const COLORS = ['var(--theme-500)', 'var(--theme-400)', 'var(--theme-600)', 'var(--theme-300)', 'var(--theme-700)']

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2" style={{ borderColor: 'var(--theme-500)' }}></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-center" style={{
          letterSpacing: 0.5,
          background: 'var(--theme-gradient, linear-gradient(90deg, #4caf50, #22d3ee))',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          color: 'transparent'
        }}>
          Laporan Pendapatan & Pengeluaran
        </h1>
        <div className="export-buttons flex gap-2">
          <ModernButton onClick={handleExportData} icon={<Download size={16} />}>
            Export Data
          </ModernButton>
          <ModernButton onClick={handleExportImage} icon={<FileText size={16} />}>
            Export Gambar
          </ModernButton>
        </div>
      </div>

      {/* Period Selector */}
      <div className="flex flex-wrap gap-2 items-center">
        <label className="font-semibold"><Filter size={16} className="inline mr-1" />Periode:</label>
        {periodOptions.map(opt => (
          <button
            key={opt.value}
            className={`px-3 py-1 rounded-full font-medium border transition-colors ${selectedPeriod === opt.value ? 'bg-[var(--theme-500)] text-white' : 'bg-[var(--theme-50)] text-[var(--theme-700)] border-[var(--theme-200)]'}`}
            onClick={() => { setSelectedPeriod(opt.value); setUseCustomRange(false); }}
          >
            {opt.label}
          </button>
        ))}
        <label className="ml-4 flex items-center gap-1">
          <input type="checkbox" checked={useCustomRange} onChange={e => setUseCustomRange(e.target.checked)} className="accent-[var(--theme-500)]" />
          Custom Range
        </label>
        {useCustomRange && (
          <>
            <input type="date" value={customDateRange.start} onChange={e => setCustomDateRange(r => ({ ...r, start: e.target.value }))} className="ml-2 px-2 py-1 rounded border border-[var(--theme-200)]" />
            <span>-</span>
            <input type="date" value={customDateRange.end} onChange={e => setCustomDateRange(r => ({ ...r, end: e.target.value }))} className="px-2 py-1 rounded border border-[var(--theme-200)]" />
          </>
        )}
      </div>

      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {periodOptions.map(opt => {
            const data = getRevenueByPeriod(opt.value, customDateRange.start, customDateRange.end);
            const isActive = selectedPeriod === opt.value && !useCustomRange;
            return (
              <div key={opt.value} className={`card transition-shadow ${isActive ? 'ring-2 ring-[var(--theme-500)] shadow-lg' : ''}`} style={{ minHeight: 120 }}>
                <div className="p-6 flex flex-col items-center justify-center">
                  <div className="flex items-center mb-2">
                    <DollarSign className="h-7 w-7 text-green-500 mr-2" />
                    <span className="font-bold text-lg text-gray-800">{opt.label}</span>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{formatCurrency(data.totalRevenue || 0)}</p>
                    <p className="text-xs text-gray-500">Pendapatan</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        <div className="mt-4">
          <ExpenseSummaryCard
            selectedPeriod={selectedPeriod}
            customDateRange={customDateRange}
            useCustomRange={useCustomRange}
            periodOptions={periodOptions}
            className="col-span-1 md:col-span-2 lg:col-span-4"
          />
        </div>
        {/* Net Income Card */}
        <div className="mt-4">
          <div className="card" style={{ minHeight: 80, background: '#39B54A' }}>
            <div className="p-6 flex flex-col items-center justify-center">
              <div className="flex items-center mb-2">
                <TrendingUp className="h-7 w-7 text-white mr-2" />
                <span className="font-bold text-lg text-white">Pendapatan Bersih</span>
              </div>
              <div className="text-center text-white">
                {(() => {
                  // --- Duplicate ExpenseSummaryCard logic exactly ---
                  let expenses = [];
                  try {
                    const stored = localStorage.getItem('expenses');
                    expenses = stored ? JSON.parse(stored) : [];
                  } catch { expenses = []; }
                  // Filter expenses by period
                  function parseDate(dateStr) {
                    if (!dateStr) return null;
                    const [year, month, day] = dateStr.split('-').map(Number);
                    return new Date(year, month - 1, day);
                  }
                  function filterExpensesByPeriod(expenses, period, customDateRange, useCustomRange) {
                    if (!Array.isArray(expenses)) return [];
                    const now = new Date();
                    let start, end;
                    if (useCustomRange && customDateRange?.start && customDateRange?.end) {
                      start = parseDate(customDateRange.start);
                      end = parseDate(customDateRange.end);
                      end.setHours(23, 59, 59, 999);
                    } else {
                      switch (period) {
                        case 'day':
                          start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                          end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                          break;
                        case 'week': {
                          const dayOfWeek = now.getDay() || 7;
                          start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 1);
                          end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                          break;
                        }
                        case 'month':
                          start = new Date(now.getFullYear(), now.getMonth(), 1);
                          end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
                          break;
                        case 'year':
                          start = new Date(now.getFullYear(), 0, 1);
                          end = new Date(now.getFullYear() + 1, 0, 1);
                          break;
                        default:
                          start = null;
                          end = null;
                      }
                    }
                    return expenses.filter(exp => {
                      if (!exp.date) return false;
                      const d = parseDate(exp.date);
                      if (start && d < start) return false;
                      if (end && d >= end) return false;
                      return true;
                    });
                  }
                  const filteredExpenses = filterExpensesByPeriod(expenses, selectedPeriod, customDateRange, useCustomRange);
                  const totalExpense = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);
                  const totalRevenue = getRevenueByPeriod(selectedPeriod, customDateRange.start, customDateRange.end)?.totalRevenue || 0;
                  const netIncome = totalRevenue - totalExpense;
                  return (
                    <>
                      <p className="text-2xl font-bold text-white">{formatCurrency(netIncome)}</p>
                      <p className="text-xs text-white">Periode: {periodOptions.find(opt => opt.value === selectedPeriod)?.label || ''}</p>
                    </>
                  );
                })()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Revenue Trend Chart */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Tren Pendapatan</h2>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="period"
                    stroke="#666"
                    fontSize={12}
                  />
                  <YAxis
                    stroke="#666"
                    fontSize={12}
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                  />
                  <Tooltip
                    formatter={(value) => [formatCurrency(value), 'Pendapatan']}
                    labelStyle={{ color: '#333' }}
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="var(--theme-500)"
                    strokeWidth={3}
                    dot={{ fill: 'var(--theme-500)', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: 'var(--theme-500)', strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Invoice Count Chart */}
          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-semibold text-gray-900">Jumlah Invoice</h2>
            </div>
            <div className="p-6">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="period"
                    stroke="#666"
                    fontSize={12}
                  />
                  <YAxis
                    stroke="#666"
                    fontSize={12}
                  />
                  <Tooltip
                    formatter={(value) => [value, 'Jumlah Invoice']}
                    labelStyle={{ color: '#333' }}
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px'
                    }}
                  />
                  <Bar
                    dataKey="invoiceCount"
                    fill="var(--theme-500)"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Top Customers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Users className="h-5 w-5 mr-2" style={{ color: 'var(--theme-500)' }} />
                Top Pelanggan
              </h2>
            </div>
            <div className="p-6">
              {topCustomers.length > 0 ? (
                <div className="space-y-4">
                  {topCustomers.map((customer, index) => (
                    <div key={customer.customerId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm" style={{
                          background: index === 0 ? 'var(--theme-500)' :
                          index === 1 ? 'var(--theme-400)' :
                          index === 2 ? 'var(--theme-600)' : 'var(--theme-300)'
                        }}>
                          {index + 1}
                        </div>
                        <div className="ml-3">
                          <p className="font-medium text-gray-900">{customer.customerName}</p>
                          <p className="text-sm text-gray-500">{customer.invoiceCount} invoice</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold" style={{ color: 'var(--theme-600)' }}>{formatCurrency(customer.totalRevenue)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500">Belum ada data pelanggan</p>
                </div>
              )}
            </div>
          </div>

          {/* Top Customers Pie Chart */}
          <div className="card">
            <div className="card-header">
              <h2 className="text-lg font-semibold text-gray-900">Distribusi Pendapatan</h2>
            </div>
            <div className="p-6">
              {topCustomers.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={topCustomers}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={(entry) => `${entry.customerName} ${((entry.totalRevenue / reportData?.totalRevenue) * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="totalRevenue"
                    >
                      {topCustomers.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8">
                  <div className="mx-auto h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-gray-400" />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">Belum ada data untuk ditampilkan</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
  );
}

export default Reports;
