import { supabase } from './supabaseClient'
import { v4 as uuidv4 } from 'uuid'
import { ensureArray, safeParseNumber } from '../utils/errorHandling'

class HPPService {
  constructor() {
    this.storageKey = 'hpp_records';
    this.records = this.loadRecords();
  }

  loadRecords() {
    const savedRecords = localStorage.getItem(this.storageKey);
    return savedRecords ? JSON.parse(savedRecords) : [];
  }

  saveRecords() {
    localStorage.setItem(this.storageKey, JSON.stringify(this.records));
  }

  async getAllRecords() {
    try {
      // Get all HPP records first
      const { data: hppData, error: hppError } = await supabase
        .from('hpp')
        .select('*')

      if (hppError) {
        console.error('Supabase getAllRecords error:', hppError)
        return []
      }

      // Get all products
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('*')

      if (productsError) {
        console.error('Supabase getProducts error:', productsError)
      }

      // Get all ingredients
      const { data: ingredientsData, error: ingredientsError } = await supabase
        .from('bahan_baku')
        .select('*')

      if (ingredientsError) {
        console.error('Supabase getIngredients error:', ingredientsError)
      }

      const safeHppData = ensureArray(hppData);
      const safeProductsData = ensureArray(productsData);
      const safeIngredientsData = ensureArray(ingredientsData);

      // Create lookup maps for better performance
      const productsMap = safeProductsData.reduce((map, product) => {
        map[product.id] = product;
        return map;
      }, {});

      const ingredientsMap = safeIngredientsData.reduce((map, ingredient) => {
        map[ingredient.id] = ingredient;
        return map;
      }, {});

      // Group by product and aggregate ingredients
      const groupedData = safeHppData.reduce((acc, record) => {
        const productId = record.produk_id
        const product = productsMap[productId]
        const productName = product?.nama || 'Unknown Product'

        if (!acc[productId]) {
          acc[productId] = {
            id: productId,
            productName: productName,
            ingredients: [],
            hpp: 0,
            createdat: record.createdat || record.created_at || new Date().toISOString(),
            laborCost: 0,
            utilitiesCost: 0,
            packagingCost: 0,
            otherCosts: 0
          }
        }

        // Add ingredient to the product
        const ingredient = ingredientsMap[record.bahan_baku_id]
        if (ingredient) {
          acc[productId].ingredients.push({
            ingredientId: record.bahan_baku_id,
            quantity: safeParseNumber(record.jumlah, 0),
            unit: ingredient.satuan || 'g',
            harga: safeParseNumber(record.harga, 0)
          })
        }

        // Add to total HPP
        acc[productId].hpp += safeParseNumber(record.harga, 0)

        return acc
      }, {})

      // Convert to array
      return Object.values(groupedData)
    } catch (error) {
      console.error('Error in getAllRecords:', error)
      return []
    }
  }

  async getRecordById(id) {
    const { data, error } = await supabase.from('hpp').select('*').eq('id', id).single()
    if (error) {
      console.error('Supabase getRecordById error:', error)
      return null
    }
    return data
  }

  async addRecord(record) {
    try {
      // First, find or create the product
      let productId = null

      if (record.productName) {
        // Check if product exists
        const { data: existingProduct } = await supabase
          .from('products')
          .select('id')
          .eq('nama', record.productName)
          .single()

        if (existingProduct) {
          productId = existingProduct.id
        } else {
          // Create new product
          const { data: newProduct, error: productError } = await supabase
            .from('products')
            .insert([{
              id: uuidv4(),
              nama: record.productName,
              harga: record.hpp || 0,
              createdat: new Date().toISOString()
            }])
            .select()
            .single()

          if (productError) {
            console.error('Error creating product:', productError)
            return null
          }
          productId = newProduct.id
        }
      }

      // Create HPP records for each ingredient
      const hppRecords = []
      const ingredients = ensureArray(record.ingredients)

      for (const ingredient of ingredients) {
        if (ingredient.ingredientId) {
          const hppRecord = {
            id: uuidv4(),
            produk_id: productId,
            bahan_baku_id: ingredient.ingredientId,
            jumlah: safeParseNumber(ingredient.quantity, 0),
            harga: safeParseNumber(ingredient.harga, 0),
            createdat: new Date().toISOString()
          }
          hppRecords.push(hppRecord)
        }
      }

      if (hppRecords.length > 0) {
        const { data, error } = await supabase.from('hpp').insert(hppRecords).select()
        if (error) {
          console.error('Supabase addRecord error:', error)
          return null
        }
        return data
      }

      return []
    } catch (error) {
      console.error('Error in addRecord:', error)
      return null
    }
  }

  async updateRecord(productId, updates) {
    try {
      // Delete existing HPP records for this product
      await this.deleteRecord(productId)

      // Add new records with updated data
      return await this.addRecord(updates)
    } catch (error) {
      console.error('Error in updateRecord:', error)
      return null
    }
  }

  async deleteRecord(productId) {
    try {
      // Delete all HPP records for this product
      const { error } = await supabase.from('hpp').delete().eq('produk_id', productId)
      if (error) {
        console.error('Supabase deleteRecord error:', error)
        return false
      }
      return true
    } catch (error) {
      console.error('Error in deleteRecord:', error)
      return false
    }
  }

  async searchRecords(query = '') {
    const allRecords = await this.getAllRecords()
    if (!query) return allRecords

    const searchLower = query.toLowerCase();
    return allRecords.filter(record =>
      (record.productName || '').toLowerCase().includes(searchLower)
    );
  }
}

// Export a singleton instance
export const hppService = new HPPService();
