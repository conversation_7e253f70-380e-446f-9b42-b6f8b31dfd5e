import React, { useState, useEffect } from 'react'
import { Typography, Box } from '@mui/material'

const DateTimeDisplay = () => {
  const [currentDateTime, setCurrentDateTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date())
    }, 1000) // Update every second

    return () => clearInterval(timer)
  }, [])

  const formatDateTime = (date) => {
    const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
    const months = [
      '<PERSON><PERSON><PERSON>', 'Feb<PERSON><PERSON>', 'Mare<PERSON>', 'April', 'Mei', 'Juni',
      'Jul<PERSON>', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ]

    const dayName = days[date.getDay()]
    const day = date.getDate()
    const month = months[date.getMonth()]
    const year = date.getFullYear()
    
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${dayName}, ${day} ${month} ${year} | ${hours}.${minutes}.${seconds}`
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Typography 
        variant="body2" 
        sx={{ 
          fontWeight: 500,
          color: 'var(--theme-700)',
          fontSize: '0.875rem',
          fontFamily: 'inherit'
        }}
      >
        {formatDateTime(currentDateTime)}
      </Typography>
    </Box>
  )
}

export default DateTimeDisplay
